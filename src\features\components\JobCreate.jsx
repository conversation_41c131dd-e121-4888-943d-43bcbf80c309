import { useEffect, useRef, useState } from "react";
import { getProfile } from "../../api/profile";
import { transformUserData } from "../../api/userManagement";
import { getProjectById } from "../../api/projectManagement";
import { getPotentialDependentTasks } from "../../api/taskManagement";
import addIcon from "../../assets/add.svg";
import closeIcon from "../../assets/closePanel.svg";
import startDateIcon from "../../assets/creationdate.svg";
import endDateIcon from "../../assets/enddate.svg";
import fileTextIcon from "../../assets/file-text.svg";
import loadFileIcon from "../../assets/loadfile.svg";
import user1Img from "../../assets/user1.png";
import "../../styles/JobCreate.css";
import MemberAddPopup from "./MemberAddPopup";
import FollowerAddPopup from "./FollowerAddPopup";
import MemberSelectionPopup from "./MemberSelectionPopup";
import FollowerSelectionPopup from "./FollowerSelectionPopup";

// Custom Select Dropdown Component
const CustomSelect = ({
  options,
  value,
  onChange,
  placeholder,
  disabled,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const selectedOption = options.find((option) => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-select" ref={dropdownRef}>
      <div
        className={`job-form-group select ${error ? "error" : ""}`}
        onClick={toggleDropdown}
        style={{
          position: "relative",
          cursor: disabled ? "not-allowed" : "pointer",
          opacity: disabled ? 0.7 : 1,
        }}
      >
        <div
          className="select-selected"
          style={{
            padding: "6px 8px",
            border: "1px solid #bdbdbd",
            borderRadius: "4px",
            fontSize: "13px",
            fontWeight: "500",
            backgroundColor: "#fff",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: "32px",
            boxSizing: "border-box",
          }}
        >
          <span style={{ color: selectedOption ? "#5B5B5B" : "#999" }}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ marginLeft: "8px" }}
          >
            <path
              d="M6 9L12 15L18 9"
              stroke="#666666"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {isOpen && (
        <div className="job-dropdown">
          {options.map((option) => (
            <div
              key={option.value}
              className="job-dropdown-item"
              onClick={() => handleSelect(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const JobCreate = ({
  onCancel,
  onSubmit,
  disableDepartmentFilter = false,
  projectId,
  projectMembers = [],
  loadingProjectMembers = false,
  allUsers = [],
  loadingAllUsers = false,
  isSubTaskCreation = false,
  parentTask = null, // Thêm prop để nhận thông tin công việc chính
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "",
    members: [],
    followers: [],
    attachments: [], // Thêm tệp đính kèm

    isRecurring: false,
    recurringFrequency: "daily",
    weeklyDays: [],
    estimatedDays: "",
    dependentTask: "", // ID của task phụ thuộc
    dependentDelayDays: 0, // Số ngày delay
  });
  const [showMemberPopup, setShowMemberPopup] = useState(false);
  const [showFollowerPopup, setShowFollowerPopup] = useState(false);
  // State cho popup
  const [showMemberSelectionPopup, setShowMemberSelectionPopup] =
    useState(false);
  const [showFollowerSelectionPopup, setShowFollowerSelectionPopup] =
    useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "",
    members: "",
  });
  const [users, setUsers] = useState([]);
  const [userProfile, setUserProfile] = useState(null);
  const [potentialDependentTasks, setPotentialDependentTasks] = useState([]);
  const [loadingDependentTasks, setLoadingDependentTasks] = useState(false);

  // Lấy thông tin user hiện tại từ localStorage
  const getUserFromStorage = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
      // Xử lý cả trường hợp user nested và không nested
      return userRaw.user || userRaw;
    } catch (error) {
      console.error("Error parsing user from localStorage:", error);
      return {};
    }
  };

  const currentUser = userProfile || getUserFromStorage();

  // Load user profile từ API nếu localStorage không có đủ thông tin
  useEffect(() => {
    const loadUserProfile = async () => {
      const storageUser = getUserFromStorage();
      if (!storageUser.avatar || !storageUser.fullName) {
        try {
          const profileRes = await getProfile();
          const profile = profileRes.data || profileRes;
          setUserProfile(profile);
        } catch (error) {
          console.error("Error loading user profile:", error);
        }
      }
    };
    loadUserProfile();
  }, []);

  // Sử dụng projectMembers thay vì gọi getAllUsers
  useEffect(() => {
    if (projectMembers && projectMembers.length > 0) {
      // Transform projectMembers để đảm bảo format đúng
      let transformed = projectMembers.map((member) => {
        if (member.user) {
          // Nếu member có nested user object
          return transformUserData(member.user);
        } else {
          // Nếu member đã là user object
          return transformUserData(member);
        }
      });

      // Nếu đang tạo subtask, chỉ hiển thị thành viên từ parent task
      if (isSubTaskCreation && parentTask && parentTask.assignee) {
        const parentTaskMemberIds = parentTask.assignee.map(assignee => assignee.userId);
        transformed = transformed.filter(member => 
          parentTaskMemberIds.includes(member.id)
        );
      }

      setUsers(transformed);
    } else {
      setUsers([]);
    }
  }, [projectMembers, isSubTaskCreation, parentTask]);

  // Fetch potential dependent tasks khi tạo sub-task
  useEffect(() => {
    const fetchPotentialDependentTasks = async () => {
      if (isSubTaskCreation && parentTask && projectId) {
        try {
          setLoadingDependentTasks(true);
          const response = await getPotentialDependentTasks(projectId, parentTask.id);

          if (response.success) {
            
            // Transform dữ liệu để đảm bảo format đúng
            const transformedTasks = (response.data || []).map(task => {
              
              // Nếu task đã được transform rồi thì giữ nguyên
              if (task.dueDate && typeof task.dueDate === 'string' && task.dueDate.includes('/')) {
                return task;
              }
              
              // Transform từ backend format sang frontend format
              const transformedTask = {
                ...task,
                dueDate: task.dueDate ? new Date(task.dueDate).toLocaleDateString("vi-VN", { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                }) : null,
                startDate: task.startDate ? new Date(task.startDate).toLocaleDateString("vi-VN", { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                }) : null,
                endDate: task.endDate ? new Date(task.endDate).toLocaleDateString("vi-VN", { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                }) : null,
              };
              
              return transformedTask;
            });
            
            setPotentialDependentTasks(transformedTasks);
          }
        } catch (error) {
          console.error("Error fetching potential dependent tasks:", error);
          setPotentialDependentTasks([]);
        } finally {
          setLoadingDependentTasks(false);
        }
      }
    };

    fetchPotentialDependentTasks();
  }, [isSubTaskCreation, parentTask, projectId]);

  const fileInputRef = useRef(null);

  // Helper function để tạo Date an toàn
  const createSafeDate = (dateString) => {
    if (!dateString) return null;

    // Nếu là format d/m/yyyy hoặc dd/mm/yyyy thì convert sang ISO format
    if (
      typeof dateString === "string" &&
      dateString.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)
    ) {
      const [day, month, year] = dateString.split("/");
      const isoString = `${year}-${month.padStart(2, "0")}-${day.padStart(
        2,
        "0"
      )}`;
      const date = new Date(isoString);
      return isNaN(date.getTime()) ? null : date;
    }

    // Nếu là ISO string hoặc date object
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  // Helper function để lấy date string an toàn cho input min/max
  const getSafeDateString = (dateString) => {
    const date = createSafeDate(dateString);
    return date ? date.toISOString().split("T")[0] : undefined;
  };

  // Hàm tính toán thời gian dự kiến tự động
  const calculateEstimatedDays = (startDate, endDate) => {
    if (!startDate || !endDate) return "";
    const start = new Date(startDate + 'T00:00:00');
    const end = new Date(endDate + 'T00:00:00');
    const diffTime = end.getTime() - start.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const result = Math.max(1, diffDays).toString(); // Đảm bảo ít nhất 1 ngày
    console.log("calculateEstimatedDays:", { startDate, endDate, start, end, diffTime, diffDays, result });
    return result;
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Khi chuyển sang "weekly", tự động chọn tất cả các ngày làm việc
      if (field === "recurringFrequency" && value === "weekly") {
        newData.weeklyDays = [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ];
      }
      // Khi chuyển sang "daily", xóa các ngày đã chọn
      else if (field === "recurringFrequency" && value === "daily") {
        newData.weeklyDays = [];
      }

      // Xử lý khi chọn dependent task
      if (field === "dependentTask" && value && isSubTaskCreation) {
        const selectedTask = potentialDependentTasks.find(
          (task) => task.id === value
        );
        if (selectedTask) {
          // Tính ngày bắt đầu tự động = ngày kết thúc của dependent task + delay days
          // Ưu tiên sử dụng dueDate (ngày kết thúc) thay vì startDate
          let dependentEndDate = null;
          
          // Thử lấy từ dueDate trước (ngày kết thúc) - ưu tiên cao nhất
          if (selectedTask.dueDate) {
            dependentEndDate = createSafeDate(selectedTask.dueDate);
          }
          // Thử lấy từ endDate (ngày kết thúc) - ưu tiên thứ hai
          else if (selectedTask.endDate) {
            dependentEndDate = createSafeDate(selectedTask.endDate);
          }
          // Thử lấy từ các field khác có thể chứa ngày kết thúc
          else if (selectedTask.completionDate) {
            dependentEndDate = createSafeDate(selectedTask.completionDate);
          }
          else if (selectedTask.finishDate) {
            dependentEndDate = createSafeDate(selectedTask.finishDate);
          }
          // Fallback về startDate nếu không có ngày kết thúc nào
          else if (selectedTask.startDate) {
            dependentEndDate = createSafeDate(selectedTask.startDate);
          }
          
          if (dependentEndDate) {
            const delayDays = newData.dependentDelayDays || 0;
            const autoStartDate = new Date(dependentEndDate);
            autoStartDate.setDate(autoStartDate.getDate() + delayDays);

            // Cập nhật ngày bắt đầu tự động
            newData.startDate = autoStartDate.toISOString().split("T")[0];

            // Tính lại thời gian dự kiến nếu có endDate
            if (newData.endDate) {
              newData.estimatedDays = calculateEstimatedDays(
                newData.startDate,
                newData.endDate
              );
            }
          }
        }
      }

      // Xử lý khi thay đổi delay days
      if (
        field === "dependentDelayDays" &&
        formData.dependentTask &&
        isSubTaskCreation
      ) {
        const selectedTask = potentialDependentTasks.find(
          (task) => task.id === formData.dependentTask
        );
        if (selectedTask) {
          // Tính lại ngày bắt đầu tự động
          // Ưu tiên sử dụng dueDate (ngày kết thúc) thay vì startDate
          let dependentEndDate = null;
          
          // Thử lấy từ dueDate trước (ngày kết thúc) - ưu tiên cao nhất
          if (selectedTask.dueDate) {
            dependentEndDate = createSafeDate(selectedTask.dueDate);
          }
          // Thử lấy từ endDate (ngày kết thúc) - ưu tiên thứ hai
          else if (selectedTask.endDate) {
            dependentEndDate = createSafeDate(selectedTask.endDate);
          }
          // Thử lấy từ các field khác có thể chứa ngày kết thúc
          else if (selectedTask.completionDate) {
            dependentEndDate = createSafeDate(selectedTask.completionDate);
          }
          else if (selectedTask.finishDate) {
            dependentEndDate = createSafeDate(selectedTask.finishDate);
          }
          // Fallback về startDate nếu không có ngày kết thúc nào
          else if (selectedTask.startDate) {
            dependentEndDate = createSafeDate(selectedTask.startDate);
          }
          
          if (dependentEndDate) {
            const delayDays = parseInt(value) || 0;
            const autoStartDate = new Date(dependentEndDate);
            autoStartDate.setDate(autoStartDate.getDate() + delayDays);

            // Cập nhật ngày bắt đầu tự động
            newData.startDate = autoStartDate.toISOString().split("T")[0];

            // Tính lại thời gian dự kiến nếu có endDate
            if (newData.endDate) {
              newData.estimatedDays = calculateEstimatedDays(
                newData.startDate,
                newData.endDate
              );
            }
          }
        }
      }

      // Tự động tính toán thời gian dự kiến khi thay đổi ngày
      if (field === "startDate" || field === "endDate") {
        const startDate = field === "startDate" ? value : prev.startDate;
        const endDate = field === "endDate" ? value : prev.endDate;
        newData.estimatedDays = calculateEstimatedDays(startDate, endDate);

        // Kiểm tra và tự động điều chỉnh tần suất lặp lại dựa trên thời gian công việc
        const newDuration = parseInt(
          calculateEstimatedDays(startDate, endDate)
        );
        if (prev.isRecurring && newDuration) {
          // Nếu công việc >= 7 ngày thì tắt lặp lại
          if (newDuration >= 7) {
            newData.isRecurring = false;
            newData.recurringFrequency = "daily";
            newData.weeklyDays = [];
          }
          // Nếu công việc nhiều hơn 1 ngày và đang chọn daily, chuyển sang weekly
          else if (newDuration > 1 && prev.recurringFrequency === "daily") {
            newData.recurringFrequency = "weekly";
            newData.weeklyDays = [
              "monday",
              "tuesday",
              "wednesday",
              "thursday",
              "friday",
              "saturday",
              "sunday",
            ];
          }
          // Công việc 1 ngày có thể giữ nguyên cả daily và weekly
        }
      }

      return newData;
    });

    // Clear error when user starts typing
    if (error) setError("");
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }

    // Real-time validation cho ngày
    if (field === "startDate" || field === "endDate") {
      const newErrors = { ...fieldErrors };

      // Validation cho ngày bắt đầu
      if (field === "startDate" && value) {
        const today = new Date().toISOString().split("T")[0];
        if (value < today) {
          newErrors.startDate = "Thời gian bắt đầu không thể là quá khứ";
        } else if (isSubTaskCreation && parentTask && parentTask.startDate) {
          // Validation cho sub-task dựa trên parent task
          const taskStartDate = new Date(value + 'T00:00:00');
          const parentStartDate = createSafeDate(parentTask.startDate);
          if (parentStartDate && taskStartDate < parentStartDate) {
            newErrors.startDate =
              "Ngày bắt đầu không được sớm hơn ngày bắt đầu của công việc chính";
          } else {
            delete newErrors.startDate;
          }
        } else if (!isSubTaskCreation && projectInfo && projectInfo.startDate) {
          // Validation cho main task dựa trên project
          const taskStartDate = new Date(value + 'T00:00:00');
          const projectStartDate = createSafeDate(projectInfo.startDate);
          if (projectStartDate && taskStartDate < projectStartDate) {
            newErrors.startDate =
              "Ngày bắt đầu công việc không thể trước ngày bắt đầu dự án";
          } else {
            delete newErrors.startDate;
          }
        } else {
          delete newErrors.startDate;
        }
      }

      // Validation cho ngày kết thúc
      if (field === "endDate" && value) {
        const today = new Date().toISOString().split("T")[0];
        if (value < today) {
          newErrors.endDate = "Thời gian kết thúc không thể là quá khứ";
        } else if (
          isSubTaskCreation &&
          parentTask &&
          (parentTask.dueDate || parentTask.endDate)
        ) {
          // Validation cho sub-task dựa trên parent task
          const taskEndDate = new Date(value + 'T00:00:00');
          const parentEndDate = createSafeDate(parentTask.dueDate || parentTask.endDate);
          if (parentEndDate && taskEndDate > parentEndDate) {
            newErrors.endDate =
              "Ngày kết thúc không được muộn hơn ngày kết thúc của công việc chính";
          } else {
            delete newErrors.endDate;
          }
        } else if (!isSubTaskCreation && projectInfo && projectInfo.endDate) {
          // Validation cho main task dựa trên project
          const taskEndDate = new Date(value + 'T00:00:00');
          const projectEndDate = createSafeDate(projectInfo.endDate);
          if (projectEndDate && taskEndDate > projectEndDate) {
            newErrors.endDate =
              "Ngày kết thúc công việc không thể sau ngày kết thúc dự án";
          } else {
            delete newErrors.endDate;
          }
        } else {
          delete newErrors.endDate;
        }
      }

      // Validation cho startDate phải trước endDate ít nhất 1 ngày
      if (formData.startDate && value && field === "endDate") {
        const startDate = new Date(formData.startDate + 'T00:00:00');
        const endDate = new Date(value + 'T00:00:00');
        const diffTime = endDate.getTime() - startDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (startDate > endDate) {
          newErrors.endDate =
            "Ngày kết thúc phải sau hoặc bằng ngày bắt đầu";
        }
      }
      if (formData.endDate && value && field === "startDate") {
        const startDate = new Date(value + 'T00:00:00');
        const endDate = new Date(formData.endDate + 'T00:00:00');
        const diffTime = endDate.getTime() - startDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (startDate > endDate) {
          newErrors.startDate =
            "Ngày bắt đầu phải trước hoặc bằng ngày kết thúc";
        }
      }

      setFieldErrors(newErrors);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Reset field errors
    setFieldErrors({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      priority: "",
      members: "",
    });

    let hasErrors = false;
    const newFieldErrors = {};

    // Validation
    if (!formData.name.trim()) {
      newFieldErrors.name = "Tên công việc không được để trống";
      hasErrors = true;
    }

    // Validation cho mô tả - không bắt buộc, chỉ kiểm tra độ dài nếu có
    if (
      formData.description.trim() &&
      formData.description.trim().split(/\s+/).length > 1000
    ) {
      newFieldErrors.description = "Mô tả không được vượt quá 1000 từ";
      hasErrors = true;
    }

    const today = new Date().toISOString().split("T")[0]; // Format YYYY-MM-DD

    if (!formData.startDate) {
      newFieldErrors.startDate = "Vui lòng chọn thời gian bắt đầu";
      hasErrors = true;
    } else if (formData.startDate < today) {
      newFieldErrors.startDate = "Thời gian bắt đầu không thể là quá khứ";
      hasErrors = true;
    }

    if (!formData.endDate) {
      newFieldErrors.endDate = "Vui lòng chọn thời gian kết thúc";
      hasErrors = true;
    } else if (formData.endDate < today) {
      newFieldErrors.endDate = "Thời gian kết thúc không thể là quá khứ";
      hasErrors = true;
    }

    // Validation cho ngày kết thúc phải hơn ngày bắt đầu ít nhất 1 ngày
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate + 'T00:00:00');
      const endDate = new Date(formData.endDate + 'T00:00:00');

      if (startDate > endDate) {
        newFieldErrors.endDate =
          "Ngày kết thúc phải sau hoặc bằng ngày bắt đầu";
        hasErrors = true;
      }
    }

    // Validation cho timeline
    if (isSubTaskCreation && parentTask) {
      // Validation cho sub-task dựa trên parent task timeline
      if (formData.startDate && parentTask.startDate) {
        const taskStartDate = new Date(formData.startDate + 'T00:00:00');
        const parentStartDate = createSafeDate(parentTask.startDate);
        if (parentStartDate && taskStartDate < parentStartDate) {
          newFieldErrors.startDate =
            "Ngày bắt đầu không được sớm hơn ngày bắt đầu của công việc chính";
          hasErrors = true;
        }
      }

      if (formData.endDate && (parentTask.dueDate || parentTask.endDate)) {
        const taskEndDate = new Date(formData.endDate + 'T00:00:00');
        const parentEndDate = createSafeDate(parentTask.dueDate || parentTask.endDate);
        if (parentEndDate && taskEndDate > parentEndDate) {
          newFieldErrors.endDate =
            "Ngày kết thúc không được muộn hơn ngày kết thúc của công việc chính";
          hasErrors = true;
        }
      }
    } else if (!isSubTaskCreation && projectInfo) {
      // Validation cho main task dựa trên project timeline
      if (formData.startDate && projectInfo.startDate) {
        const taskStartDate = new Date(formData.startDate + 'T00:00:00');
        const projectStartDate = createSafeDate(projectInfo.startDate);
        if (projectStartDate && taskStartDate < projectStartDate) {
          newFieldErrors.startDate =
            "Ngày bắt đầu công việc không thể trước ngày bắt đầu dự án";
          hasErrors = true;
        }
      }

      if (formData.endDate && projectInfo.endDate) {
        const taskEndDate = new Date(formData.endDate + 'T00:00:00');
        const projectEndDate = createSafeDate(projectInfo.endDate);
        if (projectEndDate && taskEndDate > projectEndDate) {
          newFieldErrors.endDate =
            "Ngày kết thúc công việc không thể sau ngày kết thúc dự án";
          hasErrors = true;
        }
      }
    }

    if (!formData.priority) {
      newFieldErrors.priority = "Vui lòng chọn mức độ ưu tiên";
      hasErrors = true;
    }

    // Validation cho thành viên - bắt buộc phải có ít nhất 1 thành viên
    if (!formData.members || formData.members.length === 0) {
      newFieldErrors.members = isSubTaskCreation 
        ? "Vui lòng chọn 1 thành viên thực hiện từ công việc chính" 
        : "Vui lòng thêm ít nhất một thành viên";
      hasErrors = true;
    }
    // Validation cho subtask - chỉ được chọn 1 thành viên
    if (isSubTaskCreation && formData.members && formData.members.length > 1) {
      newFieldErrors.members = "Chỉ được chọn 1 thành viên thực hiện";
      hasErrors = true;
    }

    // Validate thời gian dự kiến
    if (
      formData.estimatedDays === "" ||
      isNaN(Number(formData.estimatedDays)) ||
      Number(formData.estimatedDays) <= 0
    ) {
      newFieldErrors.estimatedDays =
        "Vui lòng nhập thời gian dự kiến hợp lệ (>0)";
      hasErrors = true;
    }

    // Validation cho tính năng lặp lại (chỉ cho task chính, không cho task phụ)
    if (!isSubTaskCreation && formData.isRecurring) {
      const taskDuration = getTaskDurationDays();

      // Chỉ cho phép lặp lại nếu công việc dưới 7 ngày
      if (taskDuration >= 7) {
        newFieldErrors.isRecurring =
          "Chỉ công việc có thời hạn dưới 7 ngày mới được phép lặp lại";
        hasErrors = true;
      }

      // Kiểm tra tần suất lặp lại
      if (!formData.recurringFrequency) {
        newFieldErrors.recurringFrequency = "Vui lòng chọn tần suất lặp lại";
        hasErrors = true;
      } else {
        // Công việc 1 ngày có thể chọn cả daily và weekly

        // Nếu công việc nhiều hơn 1 ngày mà chọn daily
        if (taskDuration > 1 && formData.recurringFrequency === "daily") {
          newFieldErrors.recurringFrequency =
            "Công việc nhiều hơn 1 ngày chỉ được phép lặp lại hàng tuần";
          hasErrors = true;
        }

        // Nếu chọn weekly thì phải có ít nhất 1 ngày trong tuần
        if (
          formData.recurringFrequency === "weekly" &&
          (!formData.weeklyDays || formData.weeklyDays.length === 0)
        ) {
          newFieldErrors.weeklyDays =
            "Vui lòng chọn ít nhất một ngày trong tuần";
          hasErrors = true;
        }
      }
    }

    if (hasErrors) {
      setFieldErrors(newFieldErrors);
      return;
    }

    try {
      setLoading(true);
      setError("");
      // Map FE -> BE
      const priorityMap = {
        medium: "medium",
        high: "high",
        low: "low",
        urgent: "urgent",
      };
      // Lấy ID của user hiện tại với nhiều fallback
      const createdById =
        currentUser._id ||
        currentUser.id ||
        (currentUser.user && currentUser.user._id) ||
        (currentUser.user && currentUser.user.id) ||
        "unknown_user";

      // Tạo danh sách assignedToIds từ formData.members
      const assignedToIds = formData.members
        .map((member) => member.id || member.userId)
        .filter(Boolean);

      // Tạo danh sách followerIds từ formData.followers
      const followerIds = formData.followers
        .map((follower) => follower.id || follower.userId)
        .filter(Boolean);

      // Map weeklyDays từ string sang number
      const dayMap = {
        monday: 1,
        tuesday: 2,
        wednesday: 3,
        thursday: 4,
        friday: 5,
        saturday: 6,
        sunday: 0,
      };

      const repeatDays =
        formData.isRecurring && formData.recurringFrequency === "weekly"
          ? formData.weeklyDays
              .map((day) => dayMap[day])
              .filter((num) => num !== undefined)
          : [];

      const jobData = {
        title: formData.name.trim(),
        description: formData.description.trim(),
        projectId: projectId,
        assignedToIds: assignedToIds, // Gửi array thay vì single ID
        assignedToId: assignedToIds[0] || "", // backward compatibility
        watchers: followerIds, // Backend expect 'watchers', không phải 'followerIds'
        followerIds: followerIds, // Cũng gửi followerIds để đảm bảo tương thích
        createdById,
        status: "pending", // Mặc định là pending (đang chờ)
        priority: priorityMap[formData.priority] || "medium",
        dueDate: new Date(formData.endDate + 'T00:00:00').toISOString(),
        startDate: new Date(formData.startDate + 'T00:00:00').toISOString(),
        // Thêm thông tin lặp lại công việc - map sang backend format (chỉ cho task chính)
        isRepeat: isSubTaskCreation ? false : formData.isRecurring,
        repeatType: isSubTaskCreation
          ? null
          : formData.isRecurring
          ? formData.recurringFrequency
          : null,
        repeatDays: isSubTaskCreation ? [] : repeatDays,
        // Thêm thông tin members để có thể sử dụng trong WorkContent
        membersData: formData.members, // Gửi thêm thông tin đầy đủ về members
        followersData: formData.followers, // Gửi thêm thông tin đầy đủ về followers
        // Thêm original status và priority để hiển thị đúng trong UI
        originalStatus: "waiting", // Status gốc mặc định là waiting
        originalPriority: formData.priority, // Priority gốc từ form để hiển thị
        // Thêm timestamp để đảm bảo công việc mới nằm đầu danh sách
        createdAt: new Date().toISOString(),
        // Thêm tệp đính kèm để WorkContent có thể upload sau khi tạo task
        // Gửi danh sách tệp đính kèm để upload riêng
        // Thêm thông tin cho sub-task creation
        dependentTask: isSubTaskCreation ? formData.dependentTask : null,

        // Thêm thông tin dependent task cho backend
        parentTaskId: isSubTaskCreation && parentTask ? parentTask.id : null,
        dependentTaskId:
          isSubTaskCreation && formData.dependentTask
            ? formData.dependentTask
            : null,
        dependentDelayDays:
          isSubTaskCreation && formData.dependentTask
            ? formData.dependentDelayDays || 0
            : 0,

        estimatedDays: formData.estimatedDays,
        duration: formData.estimatedDays, // Thêm duration để đảm bảo backend nhận được

        // Thêm thông tin tệp đính kèm
        attachments: formData.attachments, // Gửi danh sách tệp đính kèm
      };

      console.log("JobCreate - formData.estimatedDays:", formData.estimatedDays);
      console.log("JobCreate - jobData.estimatedDays:", jobData.estimatedDays);
      if (onSubmit) {
        await onSubmit(jobData);
      }
    } catch (error) {
      console.error("Error creating job:", error);
      setError("Có lỗi xảy ra khi tạo công việc");
    } finally {
      setLoading(false);
    }
  };

  // Handler cho thành viên
  const handleAddMember = () => setShowMemberSelectionPopup(true);
  const handleMemberAdded = (member) => {
    if (isSubTaskCreation) {
      // Với subtask, chỉ cho phép 1 thành viên - thay thế thành viên hiện tại
      setFormData((prev) => ({ ...prev, members: [member] }));
    } else {
      // Với task chính, cho phép nhiều thành viên
      setFormData((prev) => ({ ...prev, members: [...prev.members, member] }));
    }
    setShowMemberSelectionPopup(false);
  };
  const handleRemoveMember = (index) => {
    setFormData((prev) => ({
      ...prev,
      members: prev.members.filter((_, i) => i !== index),
    }));
  };
  // Handler cho người theo dõi
  const handleAddFollower = () => setShowFollowerSelectionPopup(true);
  const handleFollowerAdded = (follower) => {
    setFormData((prev) => ({
      ...prev,
      followers: [...prev.followers, follower],
    }));
    setShowFollowerSelectionPopup(false);
  };
  const handleRemoveFollower = (index) => {
    setFormData((prev) => ({
      ...prev,
      followers: prev.followers.filter((_, i) => i !== index),
    }));
  };

  const handleWeeklyDayChange = (dayValue, isChecked) => {
    setFormData((prev) => ({
      ...prev,
      weeklyDays: isChecked
        ? [...prev.weeklyDays, dayValue]
        : prev.weeklyDays.filter((day) => day !== dayValue),
    }));
  };

  // Xử lý tệp đính kèm
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    // Lọc bỏ các file trùng tên
    const newFiles = files.filter(newFile =>
      !formData.attachments.some(existingFile => existingFile.name === newFile.name)
    );
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newFiles]
    }));
    // Reset input để có thể chọn lại cùng file
    e.target.value = '';
  };

  const removeFile = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  // Chuyển đổi các lựa chọn priority thành định dạng options cho CustomSelect
  const priorityOptions = [
    { value: "low", label: "Thấp" },
    { value: "medium", label: "Trung bình" },
    { value: "high", label: "Cao" },
  ];

  // Tạo options cho dependent tasks
  const dependentTaskOptions = potentialDependentTasks.map((task) => ({
    value: task.id,
    label: `${task.title} (${task.duration} ngày)`,
    task: task,
  }));

  // Sau khi lấy startDate và endDate từ formData
  const getTaskDurationDays = () => {
    if (!formData.startDate || !formData.endDate) return 0;
    const start = new Date(formData.startDate);
    const end = new Date(formData.endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.max(1, Math.ceil(diffTime / (1000 * 60 * 60 * 24))); // Đảm bảo ít nhất 1 ngày
  };
  const taskDuration = getTaskDurationDays();
  const canRepeat = taskDuration < 7; // Chỉ công việc dưới 7 ngày mới được lặp
  const canRepeatDaily = taskDuration === 1; // Chỉ công việc 1 ngày mới được lặp hàng ngày
  const canRepeatWeekly = taskDuration >= 1 && taskDuration < 7; // Công việc 1-6 ngày đều có thể lặp hàng tuần

  // Lấy thông tin phòng ban của dự án từ projectId
  const [projectDepartmentId, setProjectDepartmentId] = useState(null);
  const [projectInfo, setProjectInfo] = useState(null);

  // Fetch thông tin dự án để lấy departmentId và thông tin dự án
  useEffect(() => {
    if (projectId) {
      const fetchProjectInfo = async () => {
        try {
          const response = await getProjectById(projectId);

          if (response.success && response.data) {
            setProjectDepartmentId(response.data.departmentId);
            setProjectInfo(response.data);
          }
        } catch (error) {
          console.error("Error fetching project info:", error);
        }
      };
      fetchProjectInfo();
    }
  }, [projectId]);

  return (
    <div className="job-create-panel-backdrop" onClick={onCancel}>
      <div
        className="job-create-panel-container"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="job-create-panel-header">
          <span className="job-create-panel-title">
            {isSubTaskCreation ? "Tạo công việc phụ" : "Tạo công việc chính"}
          </span>
          <button className="job-panel-close-btn" onClick={onCancel}>
            <img src={closeIcon} alt="Đóng" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="job-create-panel-form">
          {error && <div className="error-message-panel">{error}</div>}
          <div className="job-panel-section">
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Tên công việc</div>
                <div className="job-panel-value">
                  <input
                    type="text"
                    placeholder="Nhập tên công việc"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className={`job-create-input ${
                      fieldErrors.name ? "error" : ""
                    }`}
                  />
                </div>
              </div>
              {fieldErrors.name && (
                <div className="job-create-field-error">{fieldErrors.name}</div>
              )}
            </div>

            <div className="job-panel-row">
              <div className="job-panel-label">Người tạo</div>
              <div className="job-panel-value">
                <img
                  src={
                    currentUser.avatar || currentUser.profilePicture || user1Img
                  }
                  alt="avatar"
                  className="job-panel-avatar"
                />
                <span className="job-panel-label">
                  {currentUser.fullName ||
                    currentUser.name ||
                    currentUser.username ||
                    currentUser.email ||
                    "Admin"}
                </span>
              </div>
            </div>

            {projectInfo && (
              <div
                className="job-panel-row">
                <div
                  className="job-panel-label">
                  Thông tin dự án:
                </div>
                <div
                  className="job-panel-value" style={{ fontSize: "14px", color: "#5b5b5b" }}>
                  Dự án: {projectInfo.name} ({projectInfo.projectCode || "N/A"})
                  <br />
                  Timeline:{" "}
                  {projectInfo.startDate
                    ? new Date(projectInfo.startDate).toLocaleDateString(
                        "vi-VN"
                      )
                    : "N/A"}{" "}
                  -{" "}
                  {projectInfo.endDate
                    ? new Date(projectInfo.endDate).toLocaleDateString("vi-VN")
                    : "N/A"}
                </div>
              </div>
            )}

            {/*---------------- Trường thêm người theo dõi -------------------------- */}
            {!isSubTaskCreation && (
              <div className="job-create-field-wrapper">
                <div className="job-panel-row">
                  <div className="job-panel-label">Người theo dõi</div>
                  <div className="job-panel-value job-panel-members-flex-row">
                    <button
                      type="button"
                      className="job-panel-add-member-btn"
                      onClick={handleAddFollower}
                    >
                      <img src={addIcon} alt="Thêm người theo dõi" />
                    </button>
                    <div className="job-panel-members-list">
                      {formData.followers.map((follower, index) => (
                        <span key={index} className="job-panel-member">
                          <img
                            src={follower.avatar}
                            alt={follower.name}
                            className="job-panel-avatar"
                          />
                          <button
                            type="button"
                            className="job-panel-remove-member-btn"
                            onClick={() => handleRemoveFollower(index)}
                            title="Xóa người theo dõi"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/*---------------- Trường thêm thành viên -------------------------- */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">
                  {isSubTaskCreation ? "Thành viên thực hiện" : "Thành viên"}
                </div>
                <div className="job-panel-value job-panel-members-flex-row">
                  <button
                    type="button"
                    className="job-panel-add-member-btn"
                    onClick={handleAddMember}
                    disabled={isSubTaskCreation && formData.members.length >= 1}
                  >
                    <img src={addIcon} alt={isSubTaskCreation ? "Chọn thành viên thực hiện" : "Thêm thành viên"} />
                  </button>
                  <div className="job-panel-members-list">
                    {formData.members.map((member, index) => (
                      <span key={index} className="job-panel-member">
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="job-panel-avatar"
                        />
                        <button
                          type="button"
                          className="job-panel-remove-member-btn"
                          onClick={() => handleRemoveMember(index)}
                          title="Xóa thành viên"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              {isSubTaskCreation && formData.members.length === 0 && (
                <div style={{ fontSize: "12px", color: "#666", marginTop: "4px" }}>
                  Chỉ có thể chọn 1 thành viên từ danh sách thành viên của công việc chính
                </div>
              )}
              {fieldErrors.members && (
                <div className="job-create-field-error">
                  {fieldErrors.members}
                </div>
              )}
            </div>

            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Mức độ ưu tiên</div>
                <div className="job-panel-value">
                  <CustomSelect
                    options={priorityOptions}
                    value={formData.priority}
                    onChange={(value) => handleInputChange("priority", value)}
                    placeholder="Chọn mức độ ưu tiên"
                    error={fieldErrors.priority}
                  />
                </div>
              </div>
              {fieldErrors.priority && (
                <div className="job-create-field-error">
                  {fieldErrors.priority}
                </div>
              )}
            </div>

            {/* Conditional fields for sub-task creation */}
            {isSubTaskCreation && (
              <>
                <div className="job-create-field-wrapper">
                  <div className="job-panel-row">
                    <div className="job-panel-label">Nhiệm vụ phụ thuộc</div>
                    <div className="job-panel-value">
                      <CustomSelect
                        options={[
                          { value: "", label: "Không có nhiệm vụ phụ thuộc" },
                          ...dependentTaskOptions,
                        ]}
                        value={formData.dependentTask}
                        onChange={(value) =>
                          handleInputChange("dependentTask", value)
                        }
                        placeholder="Chọn nhiệm vụ phụ thuộc"
                        disabled={loadingDependentTasks}
                      />
                    </div>
                  </div>
                  {loadingDependentTasks && (
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#666",
                        marginTop: "4px",
                      }}
                    >
                      Đang tải danh sách nhiệm vụ...
                    </div>
                  )}
                  {!loadingDependentTasks &&
                    potentialDependentTasks.length === 0 && (
                      <div
                        style={{
                          fontSize: "12px",
                          color: "#666",
                          marginTop: "4px",
                        }}
                      >
                        Chưa có nhiệm vụ nào khác trong task này
                      </div>
                    )}
                </div>

                {/* Hiển thị trường delay days khi đã chọn dependent task */}
                {formData.dependentTask && (
                  <div className="job-create-field-wrapper">
                    <div className="job-panel-row">
                      <div className="job-panel-label">
                        Số ngày chờ sau khi hoàn thành
                      </div>
                      <div className="job-panel-value">
                        <input
                          type="number"
                          min="0"
                          placeholder="0"
                          value={formData.dependentDelayDays}
                          onChange={(e) =>
                            handleInputChange(
                              "dependentDelayDays",
                              e.target.value
                            )
                          }
                          className="job-create-input"
                          style={{ width: "100%" }}
                        />
                      </div>
                    </div>
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#666",
                        marginTop: "4px",
                      }}
                    >
                      Nhiệm vụ này sẽ bắt đầu sau{" "}
                      {formData.dependentDelayDays || 0} ngày kể từ khi nhiệm vụ
                      phụ thuộc hoàn thành
                    </div>
                  </div>
                )}
              </>
            )}

            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Ngày bắt đầu</div>
                <div className="job-panel-value">
                  <div
                    className={`job-create-date-input-group-custom ${
                      fieldErrors.startDate ? "error" : ""
                    }`}
                    style={{ width: "100%", cursor: "pointer" }}
                    onClick={() =>
                      document
                        .querySelector('input[name="jobStartDate"]')
                        ?.showPicker?.()
                    }
                  >
                    <img
                      src={startDateIcon}
                      alt="calendar"
                      className="job-calendar-icon-custom"
                    />
                    <input
                      name="jobStartDate"
                      type="date"
                      value={formData.startDate}
                      min={(() => {
                        const today = new Date().toISOString().split("T")[0];
                        if (
                          isSubTaskCreation &&
                          parentTask &&
                          parentTask.startDate
                        ) {
                          const parentStartDateStr = getSafeDateString(
                            parentTask.startDate
                          );
                          return parentStartDateStr &&
                            parentStartDateStr > today
                            ? parentStartDateStr
                            : today;
                        } else if (
                          !isSubTaskCreation &&
                          projectInfo &&
                          projectInfo.startDate
                        ) {
                          const projectStartDateStr = getSafeDateString(
                            projectInfo.startDate
                          );
                          return projectStartDateStr &&
                            projectStartDateStr > today
                            ? projectStartDateStr
                            : today;
                        }
                        return today;
                      })()}
                      max={(() => {
                        if (
                          isSubTaskCreation &&
                          parentTask &&
                          (parentTask.dueDate || parentTask.endDate)
                        ) {
                          return getSafeDateString(
                            parentTask.dueDate || parentTask.endDate
                          );
                        } else if (
                          !isSubTaskCreation &&
                          projectInfo &&
                          projectInfo.endDate
                        ) {
                          return getSafeDateString(projectInfo.endDate);
                        }
                        return undefined;
                      })()}
                      onChange={(e) =>
                        handleInputChange("startDate", e.target.value)
                      }
                      placeholder="Chọn ngày bắt đầu"
                      className="job-create-date-custom"
                      style={{ cursor: "pointer" }}
                      readOnly={isSubTaskCreation && formData.dependentTask}
                    />
                  </div>
                </div>
              </div>
              {isSubTaskCreation && formData.dependentTask && (
                <div
                  style={{
                    fontSize: "12px",
                    color: "#0066cc",
                    marginTop: "4px",
                  }}
                >
                  Ngày bắt đầu được tính tự động dựa trên nhiệm vụ phụ thuộc
                </div>
              )}
              {fieldErrors.startDate && (
                <div className="job-create-field-error">
                  {fieldErrors.startDate}
                </div>
              )}
            </div>

            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Ngày kết thúc</div>
                <div className="job-panel-value">
                  <div
                    className={`job-create-date-input-group-custom ${
                      fieldErrors.endDate ? "error" : ""
                    }`}
                    style={{ width: "100%", cursor: "pointer" }}
                    onClick={() =>
                      document
                        .querySelector('input[name="jobEndDate"]')
                        ?.showPicker?.()
                    }
                  >
                    <img
                      src={endDateIcon}
                      alt="calendar"
                      className="job-calendar-icon-custom"
                    />
                    <input
                      name="jobEndDate"
                      type="date"
                      value={formData.endDate}
                      min={(() => {
                        if (formData.startDate) return formData.startDate;

                        const today = new Date().toISOString().split("T")[0];
                        if (
                          isSubTaskCreation &&
                          parentTask &&
                          parentTask.startDate
                        ) {
                          const parentStartDateStr = getSafeDateString(
                            parentTask.startDate
                          );
                          return parentStartDateStr &&
                            parentStartDateStr > today
                            ? parentStartDateStr
                            : today;
                        } else if (
                          !isSubTaskCreation &&
                          projectInfo &&
                          projectInfo.startDate
                        ) {
                          const projectStartDateStr = getSafeDateString(
                            projectInfo.startDate
                          );
                          return projectStartDateStr &&
                            projectStartDateStr > today
                            ? projectStartDateStr
                            : today;
                        }
                        return today;
                      })()}
                      max={(() => {
                        if (
                          isSubTaskCreation &&
                          parentTask &&
                          (parentTask.dueDate || parentTask.endDate)
                        ) {
                          return getSafeDateString(
                            parentTask.dueDate || parentTask.endDate
                          );
                        } else if (
                          !isSubTaskCreation &&
                          projectInfo &&
                          projectInfo.endDate
                        ) {
                          return getSafeDateString(projectInfo.endDate);
                        }
                        return undefined;
                      })()}
                      onChange={(e) =>
                        handleInputChange("endDate", e.target.value)
                      }
                      placeholder="Chọn ngày kết thúc"
                      className="job-create-date-custom"
                      style={{ cursor: "pointer" }}
                    />
                  </div>
                </div>
              </div>
              {fieldErrors.endDate && (
                <div className="job-create-field-error">
                  {fieldErrors.endDate}
                </div>
              )}
            </div>

            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Thời gian dự kiến (ngày)</div>
                <div className="job-panel-value">
                  <input
                    type="number"
                    min="1"
                    placeholder="Tự động tính toán"
                    value={formData.estimatedDays}
                    onChange={(e) =>
                      handleInputChange("estimatedDays", e.target.value)
                    }
                    className="job-create-input"
                    style={{ width: "100%" }}
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* Hide recurring section for sub-task creation */}
            {!isSubTaskCreation && (
              <div className="job-create-field-wrapper">
                <div className="job-panel-row">
                  <div className="job-panel-label">Lặp lại (tùy chọn)</div>
                  <div className="job-panel-value">
                    <label className="ios-switch">
                      <input
                        type="checkbox"
                        checked={formData.isRecurring && canRepeat}
                        disabled={!canRepeat}
                        onChange={(e) =>
                          handleInputChange("isRecurring", e.target.checked)
                        }
                      />
                      <span className="slider"></span>
                    </label>
                  </div>
                </div>
                {formData.isRecurring && !canRepeat && (
                  <div
                    className="job-create-field-error"
                    style={{ color: "red", marginTop: 4 }}
                  >
                    Chỉ công việc có thời hạn dưới 7 ngày mới được phép lặp lại.
                  </div>
                )}
                {formData.isRecurring && canRepeat && taskDuration > 1 && (
                  <div
                    className="job-create-field-error"
                    style={{ color: "orange", marginTop: 4 }}
                  >
                    Công việc nhiều hơn 1 ngày chỉ được phép lặp lại hàng tuần.
                  </div>
                )}
              </div>
            )}

            {!isSubTaskCreation && formData.isRecurring && canRepeat && (
              <div className="job-create-field-wrapper">
                <div className="job-panel-row">
                  <div className="job-panel-label">Tần suất lặp lại</div>
                  <div className="job-panel-value">
                    <CustomSelect
                      options={[
                        {
                          value: "daily",
                          label: "Hàng ngày",
                          disabled: !canRepeatDaily,
                        },
                        {
                          value: "weekly",
                          label: "Hàng tuần",
                          disabled: !canRepeatWeekly,
                        },
                      ].filter((option) => !option.disabled)}
                      value={formData.recurringFrequency}
                      onChange={(value) =>
                        handleInputChange("recurringFrequency", value)
                      }
                      placeholder="Chọn tần suất lặp lại"
                    />
                    {fieldErrors.recurringFrequency && (
                      <div className="job-create-field-error">
                        {fieldErrors.recurringFrequency}
                      </div>
                    )}
                  </div>
                </div>
                {/* {!canRepeatDaily && formData.recurringFrequency === "daily" && (
                  <div
                    className="job-create-field-error"
                    style={{ color: "orange", marginTop: 4 }}
                  >
                    Công việc nhiều hơn 1 ngày chỉ được phép lặp lại hàng tuần.
                    Đã tự động chuyển sang hàng tuần.
                  </div>
                )} */}
              </div>
            )}

            {!isSubTaskCreation &&
              formData.isRecurring &&
              canRepeat &&
              formData.recurringFrequency === "weekly" && (
                <div className="job-create-field-wrapper">
                  <div className="job-panel-row">
                    <div className="job-panel-label">Lặp lại vào</div>
                    <div className="job-panel-value">
                      <div className="weekly-days-container">
                        {[
                          { value: "monday", label: "Thứ 2" },
                          { value: "tuesday", label: "Thứ 3" },
                          { value: "wednesday", label: "Thứ 4" },
                          { value: "thursday", label: "Thứ 5" },
                          { value: "friday", label: "Thứ 6" },
                          { value: "saturday", label: "Thứ 7" },
                          { value: "sunday", label: "Chủ nhật" },
                        ].map((day) => (
                          <label
                            key={day.value}
                            className="weekly-day-checkbox"
                          >
                            <input
                              type="checkbox"
                              checked={formData.weeklyDays.includes(day.value)}
                              onChange={(e) =>
                                handleWeeklyDayChange(
                                  day.value,
                                  e.target.checked
                                )
                              }
                            />
                            <span className="weekly-day-label">
                              {day.label}
                            </span>
                          </label>
                        ))}
                      </div>
                      {fieldErrors.weeklyDays && (
                        <div className="job-create-field-error">
                          {fieldErrors.weeklyDays}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Mô tả</div>
                <div className="job-panel-value">
                  <textarea
                    placeholder="Nhập mô tả công việc (tùy chọn)"
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange("description", e.target.value)
                    }
                    rows={3}
                    className={`job-create-textarea ${
                      fieldErrors.description ? "error" : ""
                    }`}
                  />
                </div>
              </div>
              {fieldErrors.description && (
                <div className="job-create-field-error">
                  {fieldErrors.description}
                </div>
              )}
            </div>

            {/* Phần tệp đính kèm */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-rows">
                <div className="job-panel-label">Tệp đính kèm</div>
                <div className="job-panel-value">
                  <div className="job-file-upload-section">
                    {formData.attachments.length === 0 ? (
                      <button
                        type="button"
                        onClick={handleFileSelect}
                        className="job-file-upload-area"
                      >
                        <img src={loadFileIcon} alt="Upload" className="job-loadfile-icon" />
                        <span className="job-upload-text">Bấm vào để tải lên tệp</span>
                      </button>
                    ) : (
                      <div className="job-files-container">
                        <button
                          type="button"
                          onClick={handleFileSelect}
                          className="job-add-more-files-btn"
                        >
                          <img src={addIcon} alt="Add" className="job-add-icon" />
                          <span>Thêm tệp</span>
                        </button>
                        <div className="job-selected-files">
                          {formData.attachments.map((file, index) => (
                            <div key={index} className="job-file-item">
                              <img src={fileTextIcon} alt="File" className="job-file-icon" />
                              <span className="job-file-name">{file.name}</span>
                              <button
                                type="button"
                                onClick={() => removeFile(index)}
                                className="job-remove-file-btn"
                                title="Xóa tệp"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      onChange={handleFileChange}
                      className="job-file-input-hidden"
                      accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div className="job-create-panel-footer">
          <button
            type="button"
            className="job-panel-cancel-btn"
            onClick={onCancel}
          >
            Huỷ
          </button>
          <button
            type="submit"
            className="job-panel-create-btn"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? "Đang tạo..." : "Tạo công việc"}
          </button>
        </div>
        <MemberSelectionPopup
          isOpen={showMemberSelectionPopup}
          onClose={() => setShowMemberSelectionPopup(false)}
          onAddMember={handleMemberAdded}
          existingMembers={formData.members}
          users={projectMembers.map((member) => {
            if (member.user) {
              return transformUserData(member.user);
            } else {
              return transformUserData(member);
            }
          })}
          loadingUsers={loadingProjectMembers}
          departmentId={null}
          filterByDepartment={false}
          singleSelection={isSubTaskCreation}
        />
        <FollowerSelectionPopup
          isOpen={showFollowerSelectionPopup}
          onClose={() => setShowFollowerSelectionPopup(false)}
          onAddFollower={handleFollowerAdded}
          existingFollowers={formData.followers}
          users={allUsers.filter((u) => {
            const userRole = (u.role || "").toLowerCase();
            return ["leader", "departmenthead"].includes(userRole);
          })}
          loadingUsers={loadingAllUsers}
        />
      </div>
    </div>
  );
};

export default JobCreate;
// Test file để kiểm tra hệ thống cập nhật tự động
import { triggerTaskUpdate, triggerTaskEvents, TASK_EVENTS } from './toastUtils';

// Test function để kiểm tra các events
export const testTaskUpdateSystem = () => {
  console.log('🧪 Testing Task Update System...');
  
  // Test single event
  console.log('📡 Triggering single event: taskCreated');
  triggerTaskUpdate(TASK_EVENTS.CREATED);
  
  // Test multiple events
  console.log('📡 Triggering multiple events: projectTasksUpdated, taskStatusUpdated');
  triggerTaskEvents([TASK_EVENTS.PROJECT_TASKS_UPDATED, TASK_EVENTS.STATUS_UPDATED]);
  
  // Test all events
  console.log('📡 Triggering all events');
  triggerTaskEvents(Object.values(TASK_EVENTS));
  
  console.log('✅ Task Update System test completed');
};

// Function để log events
export const logTaskEvents = () => {
  Object.values(TASK_EVENTS).forEach(eventType => {
    window.addEventListener(eventType, () => {
      console.log(`🎯 Event triggered: ${eventType}`);
    });
  });
};

// Export để sử dụng trong development
if (process.env.NODE_ENV === 'development') {
  window.testTaskUpdateSystem = testTaskUpdateSystem;
  window.logTaskEvents = logTaskEvents;
}

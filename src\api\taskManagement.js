import { API_URL, getCurrentUserEndpoints } from "./endpoints.js";

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem("token");
    const authToken = localStorage.getItem("authToken");
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: "Có lỗi xảy ra" };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || "Có lỗi xảy ra");
  }
  return response.json();
};

// Lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  return getCurrentUserEndpoints();
};

// ========== TASK MANAGEMENT FUNCTIONS ==========

// Lấy thống kê trạng thái công việc cho charts
export async function getTaskStatusStats(projectId = null) {
  try {
    // Luôn tính toán từ danh sách tasks vì backend chưa có endpoint /stats
    const tasks = projectId
      ? await getProjectTasks(projectId)
      : await getMyProjectTasks();

    const taskList = tasks.data || tasks;

    if (!Array.isArray(taskList)) {
      return {
        data: {
          pending: 0,
          in_progress: 0,
          completed: 0,
          overdue: 0,
          review: 0,
        },
      };
    }

    // Tính toán stats từ dữ liệu thực tế
    const stats = {
      pending: taskList.filter((t) => t.status === "pending").length,
      in_progress: taskList.filter((t) => t.status === "in_progress").length,
      completed: taskList.filter((t) => t.status === "completed").length,
      overdue: taskList.filter((t) => t.status === "overdue").length,
      review: taskList.filter((t) => t.status === "review").length,
    };

    return { data: stats };
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Thêm user vào project members
export async function addUserToProject(projectId, userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.ADD_PROJECT_MEMBER?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền thêm thành viên vào dự án");
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify({ userId }),
    });

    return await handleResponse(response);
  } catch (err) {
    throw err;
  }
}

// Lấy thông tin project và members cho Charts
export async function getProjectInfo(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const projectUrl = endpoints.GET_PROJECT?.(projectId);

    if (!projectUrl) {
      throw new Error("Không có quyền truy cập thông tin dự án");
    }

    const [projectResponse, membersResponse, tasksResponse] = await Promise.all(
      [
        fetch(projectUrl, {
          method: "GET",
          headers: getAuthHeaders(),
        }),
        fetch(endpoints.PROJECT_MEMBERS?.(projectId) || `${API_URL}/api/users/projects/${projectId}/members`, {
          method: "GET",
          headers: getAuthHeaders(),
        }),
        // Lấy thêm dữ liệu tasks để tính toán hiệu suất
        getProjectTasks(projectId).catch(() => ({ data: [] })),
      ]
    );

    const project = await handleResponse(projectResponse);
    const members = await handleResponse(membersResponse);
    const tasks = tasksResponse.data || tasksResponse || [];

    // Transform members data for Charts component
    const transformedMembers = (members.data || members || []).map((member) => {
      const user = member.user || member;
      const userId = user.id || user._id;

      // Tính toán tasks của user này
      const userTasks = tasks.filter((task) => {
        // Backend populate assignedToId thành assignedToId object
        if (task.assignedToId) {
          const assignedId =
            task.assignedToId._id || task.assignedToId.id || task.assignedToId;
          const userIdStr = userId.toString();
          const assignedIdStr = assignedId.toString();
          return assignedIdStr === userIdStr;
        }
        return false;
      });

      const totalTasks = userTasks.length;
      const completedTasks = userTasks.filter(
        (task) => task.status === "completed"
      ).length;
      const efficiency =
        totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      return {
        id: userId,
        name: user.fullName || user.name,
        email: user.email,
        avatar: user.avatar,
        department: user.departmentInfo?.name || user.department || "IT",
        position: user.position || user.role,
        tasks: {
          waiting: userTasks.filter((task) => task.status === "pending").length,
          inProgress: userTasks.filter((task) => task.status === "in_progress")
            .length,
          completed: completedTasks,
          review: userTasks.filter((task) => task.status === "review").length,
        },
        efficiency,
        totalTasks,
      };
    });

    return {
      project: project.data || project,
      members: transformedMembers,
    };
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách công việc của một dự án
export async function getProjectTasks(projectId, params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PROJECT_TASKS?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc của dự án");
    }

    // Thêm populate watchers để lấy thông tin người theo dõi
    const defaultParams = {
      populate: 'watchers,assignedTo,assignedToMultiple,createdBy',
      ...params
    };

    const queryString = new URLSearchParams(defaultParams).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    const result = await handleResponse(response);
    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Tạo công việc mới trong dự án
export async function createProjectTask(projectId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.CREATE_PROJECT_TASK?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền tạo công việc");
    }

    // Chuẩn bị dữ liệu task theo format API
    const formattedData = {
      title: taskData.name || taskData.title,
      description: taskData.description || "",
      priority: taskData.priority || "normal",
      status: taskData.status || "waiting",
      dueDate: taskData.dueDate,
      startDate: taskData.startDate,
      parentTaskId: taskData.parentTaskId || null,
      dependentTaskId: taskData.dependentTaskId || null,
      dependentDelayDays: taskData.dependentDelayDays || 0,
      estimatedDays: parseInt(taskData.estimatedDays) || parseInt(taskData.duration) || 1, // Chuyển thành number
      duration: parseInt(taskData.estimatedDays) || parseInt(taskData.duration) || 1, // Thêm duration để đảm bảo backend nhận được
    };

    console.log("createProjectTask - taskData.estimatedDays:", taskData.estimatedDays);
    console.log("createProjectTask - taskData.duration:", taskData.duration);
    console.log("createProjectTask - formattedData.estimatedDays:", formattedData.estimatedDays);

    // Hỗ trợ cả assignedToIds (array) và assignedToId (single) để backward compatibility
    if (taskData.assignedToIds && Array.isArray(taskData.assignedToIds)) {
      formattedData.assignedToIds = taskData.assignedToIds;
      formattedData.assignedToId = taskData.assignedToIds[0]; // backward compatibility
    } else if (taskData.assignedToId) {
      formattedData.assignedToId = taskData.assignedToId;
      formattedData.assignedToIds = [taskData.assignedToId];
    } else if (taskData.assignee?.[0]?.userId) {
      formattedData.assignedToId = taskData.assignee[0].userId;
      formattedData.assignedToIds = [taskData.assignee[0].userId];
    }

    // Thêm thông tin followers
    if (taskData.followerIds && Array.isArray(taskData.followerIds)) {
      formattedData.followerIds = taskData.followerIds;
    }

    // Thêm dữ liệu followers để có thể sử dụng trong transform
    if (taskData.followersData && Array.isArray(taskData.followersData)) {
      formattedData.followersData = taskData.followersData;
    }

    // Thêm watchers (backend field name cho followers)
    if (taskData.followerIds && Array.isArray(taskData.followerIds)) {
      formattedData.watchers = taskData.followerIds;
    }

    // Gửi request tạo task
    console.log("Sending task data to backend:", formattedData);
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(formattedData),
    });
    const result = await handleResponse(response);
    console.log("Backend response:", result);
    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Cập nhật công việc
export async function updateProjectTask(projectId, taskId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền cập nhật công việc");
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData = {};

    if (taskData.name !== undefined) updateData.title = taskData.name;
    if (taskData.description !== undefined)
      updateData.description = taskData.description;
    if (taskData.status !== undefined) updateData.status = taskData.status;
    if (taskData.priority !== undefined)
      updateData.priority = taskData.priority;
    if (taskData.dueDate !== undefined) updateData.dueDate = taskData.dueDate;
    if (taskData.startDate !== undefined)
      updateData.startDate = taskData.startDate;
    if (taskData.assignee !== undefined) {
      updateData.assignedToId =
        taskData.assignee?.[0]?.userId || taskData.assignee;
    }
    if (taskData.assignedToIds !== undefined) {
      updateData.assignedToIds = taskData.assignedToIds;
    }
    if (taskData.progress !== undefined)
      updateData.progress = taskData.progress;
    if (taskData.estimatedDays !== undefined)
      updateData.estimatedDays = taskData.estimatedDays;

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa công việc
export async function deleteProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa công việc");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Khôi phục công việc đã xóa
export async function restoreProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.RESTORE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền khôi phục công việc");
    }

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa vĩnh viễn công việc
export async function permanentDeleteProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PERMANENT_DELETE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa vĩnh viễn công việc");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách công việc đã xóa
export async function getDeletedProjectTasks(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETED_TASKS?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc đã xóa");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách nhiệm vụ phụ thuộc tiềm năng cho sub-task
export async function getPotentialDependentTasks(projectId, parentTaskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.POTENTIAL_DEPENDENT_TASKS?.(projectId, parentTaskId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập danh sách nhiệm vụ phụ thuộc");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    const result = await handleResponse(response);
    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy thống kê công việc
export async function getTaskStats(taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = `${
      endpoints.TASKS || "/api/tasks"
    }/${taskId}/stats/overview`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy tất cả project tasks mà user được assign (cho trang My Job)
export async function getMyProjectTasks(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.MY_TASKS;

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc được giao");
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== PERSONAL NOTE FUNCTIONS ==========

// Lấy ghi chú cá nhân
export async function getPersonalTasks(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PERSONAL_TASKS;

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập ghi chú cá nhân");
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Function này đã được thay thế bởi API mới trong NoteCreate component

// Cập nhật ghi chú cá nhân
export async function updatePersonalTask(taskId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_PERSONAL_TASK?.(taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền cập nhật ghi chú cá nhân");
    }

    const updateData = {};

    if (taskData.name !== undefined) updateData.title = taskData.name;
    if (taskData.description !== undefined)
      updateData.description = taskData.description;
    if (taskData.status !== undefined) updateData.status = taskData.status;
    if (taskData.priority !== undefined)
      updateData.priority = taskData.priority;
    if (taskData.dueDate !== undefined) updateData.dueDate = taskData.dueDate;
    if (taskData.startDate !== undefined)
      updateData.startDate = taskData.startDate;
    if (taskData.progress !== undefined)
      updateData.progress = taskData.progress;

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa ghi chú cá nhân
export async function deletePersonalTask(taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_PERSONAL_TASK?.(taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa ghi chú cá nhân");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách ghi chú cá nhân đã xóa (thùng rác)
export async function getDeletedPersonalTasks() {
  try {
    const response = await fetch("/api/common/personal-notes/trashed", {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Khôi phục ghi chú cá nhân đã xóa
export async function restorePersonalTask(taskId) {
  try {
    const response = await fetch(
      `/api/common/personal-notes/${taskId}/restore`,
      {
        method: "POST",
        headers: getAuthHeaders(),
      }
    );
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa vĩnh viễn ghi chú cá nhân
export async function permanentDeletePersonalTask(taskId) {
  try {
    const response = await fetch(
      `/api/common/personal-notes/${taskId}/permanent`,
      {
        method: "DELETE",
        headers: getAuthHeaders(),
      }
    );
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== UTILITY FUNCTIONS ==========

// Safe date parser that accepts ISO and dd/mm/yyyy
const parseBackendDate = (input) => {
  if (!input) return null;
  
  // Nếu đã là Date object
  if (input instanceof Date) {
    return input;
  }
  
  // Nếu là string format dd/mm/yyyy
  if (typeof input === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(input)) {
    const [d, m, y] = input.split('/');
    const iso = `${y}-${m.padStart(2,'0')}-${d.padStart(2,'0')}T00:00:00Z`;
    const dt = new Date(iso);
    return isNaN(dt.getTime()) ? null : dt;
  }
  
  // Nếu là string format yyyy-mm-dd hoặc yyyy-mm-ddTHH:mm:ss
  if (typeof input === 'string' && /^\d{4}-\d{2}-\d{2}/.test(input)) {
    const dt = new Date(input);
    return isNaN(dt.getTime()) ? null : dt;
  }
  
  // Nếu là ISO string hoặc format khác
  const dt = new Date(input);
  
  // Kiểm tra xem có phải là NaN không
  if (isNaN(dt.getTime())) {
    return null;
  }
  
  return dt;
};

// Transform dữ liệu task từ backend về format frontend
export const transformTaskData = (backendTask) => {
  // Xử lý nhiều assignees
  let assignees = [];

  // Ưu tiên assignedToMultiple (array) nếu có
  if (backendTask.assignedToMultiple && Array.isArray(backendTask.assignedToMultiple)) {
    assignees = backendTask.assignedToMultiple.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Xử lý assignedTo nếu là array
  else if (backendTask.assignedTo && Array.isArray(backendTask.assignedTo)) {
    assignees = backendTask.assignedTo.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Fallback về assignedTo hoặc assignedToId (single user)
  else if (backendTask.assignedTo || backendTask.assignedToId) {
    const user = backendTask.assignedTo || backendTask.assignedToId;
    if (user && typeof user === 'object') {
      assignees = [{
        userId: user._id || user.id,
        name: user.fullName || user.name || "Chưa có tên",
        avatar: user.avatar || user.profilePicture || "",
      }];
    }
  }

  // Xử lý followers (người theo dõi) - watchers từ backend
  let followers = [];

  // Ưu tiên followersData từ frontend nếu có
  if (backendTask.followersData && Array.isArray(backendTask.followersData)) {
    followers = backendTask.followersData.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      fullName: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Fallback về watchers từ backend (field chính trong database)
  else if (backendTask.watchers && Array.isArray(backendTask.watchers)) {
    followers = backendTask.watchers.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      fullName: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Fallback về followers từ backend (nếu có)
  else if (backendTask.followers && Array.isArray(backendTask.followers)) {
    followers = backendTask.followers.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      fullName: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Fallback về followerIds nếu có populate
  else if (backendTask.followerIds && Array.isArray(backendTask.followerIds)) {
    followers = backendTask.followerIds.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      fullName: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }

  // Helper function để parse và format ngày an toàn
  const formatDate = (dateInput) => {
    if (!dateInput) return null;
    
    // Nếu đã là string format dd/mm/yyyy thì giữ nguyên
    if (typeof dateInput === 'string' && dateInput.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      return dateInput;
    }
    
    // Parse từ backend format
    const date = parseBackendDate(dateInput);
    
    if (date && !isNaN(date.getTime())) {
      const formatted = date.toLocaleDateString("vi-VN", { 
        day: '2-digit', 
        month: '2-digit', 
        year: 'numeric' 
      });
      return formatted;
    }
    
    return null;
  };

  const transformedTask = {
    id: backendTask._id || backendTask.id || `TSK-${Date.now()}`,
    name: `${backendTask.taskCode || `TASK-${Date.now()}`} ${
      backendTask.title || "Chưa có tên"
    }`,
    status: backendTask.status || "pending",
    priority: backendTask.priority || "medium",
    assignee: assignees, // Array of assignees
    followers: followers, // Array of followers
    description: backendTask.description || "",
    dueDate: formatDate(backendTask.dueDate),
    startDate: formatDate(backendTask.startDate),
    creator:
      backendTask.createdBy || backendTask.createdById
        ? {
            name:
              (backendTask.createdBy || backendTask.createdById).fullName ||
              (backendTask.createdBy || backendTask.createdById).name ||
              "Chưa có tên",
            avatar:
              (backendTask.createdBy || backendTask.createdById).avatar || "",
          }
        : { name: "System", avatar: "" },

    activities: backendTask.activities || [],
    children: backendTask.subtasks || [],
    progress: backendTask.progress || 0,
    code: backendTask.taskCode || backendTask.code || `TASK-${Date.now()}`,
    comments: Array.isArray(backendTask.comments)
      ? backendTask.comments.length
      : 0,
    // Add project information
    projectId: (backendTask.projectId && backendTask.projectId._id) || backendTask.projectId || backendTask.project_id,
    projectCode: (backendTask.projectId && backendTask.projectId.projectCode) ||
                 backendTask.projectCode ||
                 (backendTask.project && (backendTask.project.projectCode || backendTask.project.code)) ||
                 'PRJ-N/A',
    project: backendTask.projectId || backendTask.project || null,
    // Preserve original data for debugging
    followersData: backendTask.followersData || [],
    followerIds: backendTask.followerIds || [],
    
    // Parent-child relationship
    parentTaskId: backendTask.parentTaskId ? 
      (backendTask.parentTaskId._id || backendTask.parentTaskId.id || backendTask.parentTaskId) : 
      null,
      
    // Dependencies
    dependencies: backendTask.dependentTaskId ? 
      (typeof backendTask.dependentTaskId === 'object' ? 
        [`${backendTask.dependentTaskId.taskCode || 'TASK'} ${backendTask.dependentTaskId.title || 'Chưa có tên'}`] :
        [backendTask.dependentTaskId]
      ) : 
      [],
    
    // Preserve original dependentTaskId for fallback
    dependentTaskId: backendTask.dependentTaskId,
      
    // Preserve createdAt for sorting
    createdAt: backendTask.createdAt,
    
    // Estimated days
    estimatedDays: backendTask.estimatedDays || null,
    estimatedTime: backendTask.estimatedDays ? `${backendTask.estimatedDays} ngày` : 'Không có',
    
    // Thêm thông tin dependent delay days nếu có
    dependentDelayDays: backendTask.dependentDelayDays || 0,
  };

  return transformedTask;
};

// Transform dữ liệu task list từ backend với cấu trúc parent-child
export const transformTaskListData = (backendTasks) => {
  if (!Array.isArray(backendTasks)) {
    return [];
  }



  // Transform tất cả tasks trước
  const transformedTasks = backendTasks.map((task) => transformTaskData(task));
  
  // Tách parent tasks và sub-tasks
  const parentTasks = transformedTasks.filter(task => !task.parentTaskId);
  const subTasks = transformedTasks.filter(task => task.parentTaskId);
  
  // Nhóm sub-tasks vào parent tasks
  const tasksWithChildren = parentTasks.map(parentTask => {
    const children = subTasks.filter(subTask => 
      subTask.parentTaskId && (
        subTask.parentTaskId === parentTask.id ||
        subTask.parentTaskId.toString() === parentTask.id.toString()
      )
    ).sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0)); // Sort mới nhất trước
    
    return {
      ...parentTask,
      children: children,
      showChildren: children.length > 0 // Auto expand nếu có children
    };
  });
  
  return tasksWithChildren;
};

// Kiểm tra quyền của user hiện tại với tasks
export const checkTaskPermissions = () => {
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const role = user.role || user.user?.role || "staff";

  return {
    canViewTasks: true, // Tất cả user đều có thể xem tasks
    canCreateTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    canEditTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    canDeleteTasks: ["admin", "ceo", "hr", "leader"].includes(role),
    canAssignTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    role: role,
  };
};

// Lấy task theo ID
export async function getTaskById(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc");
    }

    // Thêm populate để lấy thông tin đầy đủ
    const url = `${apiUrl}?populate=watchers,assignedTo,assignedToMultiple,createdBy`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    
    const result = await handleResponse(response);
    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== TASK ATTACHMENTS FUNCTIONS ==========

// Upload file đính kèm cho task
export async function uploadTaskAttachment(projectId, taskId, file) {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.TASK_ATTACHMENTS?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền upload file đính kèm");
    }

    const token = getAuthToken();
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Không set Content-Type để browser tự động set với boundary cho FormData
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách file đính kèm của task
export async function getTaskAttachments(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.TASK_ATTACHMENTS?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập file đính kèm");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa file đính kèm khỏi task
export async function deleteTaskAttachment(projectId, taskId, attachmentId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_TASK_ATTACHMENT?.(projectId, taskId, attachmentId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa file đính kèm");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== SUB-TASK CREATION FUNCTIONS ==========

// Tạo nhiệm vụ phụ (sub-task) cho một task cha
export async function createSubTask(projectId, parentTaskId, subTaskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.CREATE_PROJECT_TASK?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền tạo công việc");
    }

    // Chuẩn bị dữ liệu sub-task theo format API
    const formattedData = {
      title: subTaskData.title || subTaskData.name,
      description: subTaskData.description || "",
      priority: subTaskData.priority || "normal",
      status: subTaskData.status || "waiting",
      dueDate: subTaskData.dueDate,
      startDate: subTaskData.startDate,
      parentTaskId: parentTaskId, // ID của task cha
      dependentTaskId: subTaskData.dependentTaskId || null,
      dependentDelayDays: subTaskData.dependentDelayDays || 0,
      estimatedDays: subTaskData.estimatedDays,
      isRecurring: subTaskData.isRecurring || false,
      recurringFrequency: subTaskData.recurringFrequency,
      weeklyDays: subTaskData.weeklyDays,
    };

    // Hỗ trợ cả assignedToIds (array) và assignedToId (single)
    if (subTaskData.assignedToIds && Array.isArray(subTaskData.assignedToIds)) {
      formattedData.assignedToIds = subTaskData.assignedToIds;
      formattedData.assignedToId = subTaskData.assignedToIds[0];
    } else if (subTaskData.assignedToId) {
      formattedData.assignedToId = subTaskData.assignedToId;
      formattedData.assignedToIds = [subTaskData.assignedToId];
    } else if (subTaskData.membersData?.[0]?.userId) {
      formattedData.assignedToId = subTaskData.membersData[0].userId;
      formattedData.assignedToIds = [subTaskData.membersData[0].userId];
    }

    // Thêm thông tin followers
    if (subTaskData.followerIds && Array.isArray(subTaskData.followerIds)) {
      formattedData.followerIds = subTaskData.followerIds;
      formattedData.watchers = subTaskData.followerIds;
    }

    // Thêm dữ liệu followers để có thể sử dụng trong transform
    if (subTaskData.followersData && Array.isArray(subTaskData.followersData)) {
      formattedData.followersData = subTaskData.followersData;
    }

    // Gửi request tạo sub-task
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(formattedData),
    });
    
    const result = await handleResponse(response);
    
    if (result.success) {
      // Transform kết quả về format frontend
      const transformedSubTask = {
        id: result.data._id || result.data.id,
        taskCode: result.data.taskCode,
        name: `${result.data.taskCode || `SUB-${Date.now()}`} ${result.data.title || "Chưa có tên"}`,
        status: result.data.status || "waiting",
        priority: result.data.priority || "medium",
        assignee: subTaskData.membersData || [],
        description: subTaskData.description || result.data.description || "",
        dueDate: (() => {
          const dt = parseBackendDate(result.data.dueDate);
          return dt ? dt.toLocaleDateString("vi-VN", { day: '2-digit', month: '2-digit', year: 'numeric' }) : null;
        })(),
        startDate: (() => {
          const dt = parseBackendDate(result.data.startDate);
          return dt ? dt.toLocaleDateString("vi-VN", { day: '2-digit', month: '2-digit', year: 'numeric' }) : null;
        })(),
        creator: subTaskData.createdById || null,
        progress: 0,
        activities: [],
        attachments: subTaskData.attachments || [],
        parentId: parentTaskId,
        projectId: projectId,
        estimatedDays: result.data.estimatedDays || subTaskData.estimatedDays,
        dependentTask: subTaskData.dependentTask,
        dependencies: result.data.dependentTaskId ? 
          (typeof result.data.dependentTaskId === 'object' ? 
            [`${result.data.dependentTaskId.taskCode || 'TASK'} ${result.data.dependentTaskId.title || 'Chưa có tên'}`] :
            [result.data.dependentTaskId]
          ) : [],
        isRecurring: subTaskData.isRecurring,
        recurringFrequency: subTaskData.recurringFrequency,
        weeklyDays: subTaskData.weeklyDays,
        createdAt: result.data.createdAt,
        updatedAt: result.data.updatedAt,
        createdById: result.data.createdById,
        parentTaskId: parentTaskId,
        dependentDelayDays: result.data.dependentDelayDays || subTaskData.dependentDelayDays || 0
      };
      
      return {
        success: true,
        data: transformedSubTask,
        message: "Tạo nhiệm vụ phụ thành công!"
      };
    } else {
      throw new Error(result.message || 'Có lỗi xảy ra khi tạo công việc');
    }
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== TASK COMMENTS FUNCTIONS ==========

// Lấy danh sách bình luận của task
export async function getTaskComments(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.TASK_COMMENTS?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập bình luận công việc");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Gửi bình luận mới cho task
export async function postTaskComment(projectId, taskId, content) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.TASK_COMMENTS?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền gửi bình luận");
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify({ content }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== TEST FUNCTIONS ==========